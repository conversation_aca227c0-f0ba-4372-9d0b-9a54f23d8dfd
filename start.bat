@echo off
chcp 65001 >nul
echo 启动企业微信自动回复系统...

REM 检查依赖是否已安装
python -c "import PyQt5; import requests; import PIL" 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 检测到依赖未安装或不完整
    echo 请先运行 install_dependencies.bat 安装依赖
    echo.
    set /p choice="是否现在安装依赖? (y/n): "
    if /i "%choice%"=="y" (
        call install_dependencies.bat
        if %errorlevel% neq 0 exit /b 1
    ) else (
        echo 取消启动
        pause
        exit /b 1
    )
)

echo 正在启动程序...
start "" "企业微信自动回复系统.exe"
