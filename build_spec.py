#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller 打包配置生成脚本
将ntwork库作为外部依赖，不打包到exe中
"""

import os
import sys

def create_spec_file():
    """创建PyInstaller spec文件"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

import os
import sys

# 项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 数据文件列表
datas = [
    ('2.ico', '.'),  # 应用图标
    ('config-template.json', '.'),  # 配置模板
]

# 隐藏导入模块
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.sip',
    'requests',
    'PIL',
    'PIL.Image',
    'dulwich',
    'json',
    'threading',
    'uuid',
    'tempfile',
    'io',
    'random',
    'time',
]

# 排除的模块 - ntwork库不打包到exe中
excludes = [
    'ntwork',  # 主要排除ntwork库
    'ntwork.*',  # 排除ntwork的所有子模块
    'matplotlib',
    'numpy',
    'scipy',
    'pandas',
    'tkinter',
]

# 二进制文件排除
binaries = []

# 分析配置
a = Analysis(
    ['gui_app.py'],
    pathex=[project_root],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 过滤掉ntwork相关的文件
a.binaries = [x for x in a.binaries if not (x[0].startswith('ntwork') or 'ntwork' in x[0].lower())]
a.datas = [x for x in a.datas if not (x[0].startswith('ntwork') or 'ntwork' in x[0].lower())]

# 过滤掉ntwork相关的纯Python模块
a.pure = [x for x in a.pure if not (x[0].startswith('ntwork') or 'ntwork' in x[0].lower())]

# PYZ配置
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# EXE配置 - 单目录模式
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='企业微信自动回复系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='2.ico',  # 设置exe图标
)

# COLLECT配置 - 收集所有文件到单个目录
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='企业微信自动回复系统',
)
'''
    
    with open('gui_app.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("PyInstaller spec文件已创建: gui_app.spec")

if __name__ == "__main__":
    create_spec_file()
