#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的打包脚本 - 直接使用PyInstaller命令行
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_dependencies():
    """检查打包依赖"""
    print("检查打包依赖...")
    
    try:
        import PyInstaller
        print(f"PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("PyInstaller 未安装")
        print("请运行: pip install pyinstaller")
        return False
    
    try:
        import PyQt5
        print(f"PyQt5 已安装")
    except ImportError:
        print("PyQt5 未安装")
        print("请运行: pip install PyQt5")
        return False
    
    return True

def copy_ntwork_library():
    """复制ntwork库到exe目录"""
    print("复制ntwork库到exe目录...")
    
    dist_dir = Path('dist/企业微信自动回复系统')
    if not dist_dir.exists():
        print("构建目录不存在")
        return False
    
    # 查找ntwork库的安装位置
    try:
        import ntwork
        ntwork_source_path = Path(ntwork.__file__).parent
        print(f"找到ntwork库位置: {ntwork_source_path}")
        
        # 复制ntwork库到exe目录
        ntwork_target_path = dist_dir / 'ntwork'
        if ntwork_target_path.exists():
            shutil.rmtree(ntwork_target_path)
        
        shutil.copytree(ntwork_source_path, ntwork_target_path)
        print(f"复制ntwork库到: {ntwork_target_path}")
        
        return True
    except ImportError:
        print("未找到ntwork库，请先安装ntwork库")
        return False
    except Exception as e:
        print(f"复制ntwork库失败: {e}")
        return False

def build_with_pyinstaller():
    """使用PyInstaller直接构建"""
    print("开始构建应用程序...")
    
    # 清理之前的构建
    if os.path.exists('dist'):
        print("清理之前的构建文件...")
        shutil.rmtree('dist')
    
    if os.path.exists('build'):
        shutil.rmtree('build')
    
    # PyInstaller命令
    cmd = [
        sys.executable, '-m', 'PyInstaller',
        '--onedir',  # 单目录模式
        '--windowed',  # 不显示控制台
        '--name=企业微信自动回复系统',
        '--icon=2.ico',
        '--add-data=2.ico;.',
        '--add-data=config-template.json;.',
        '--exclude-module=ntwork',  # 排除ntwork模块
        '--exclude-module=matplotlib',
        '--exclude-module=numpy',
        '--exclude-module=scipy',
        '--exclude-module=pandas',
        '--exclude-module=tkinter',
        '--clean',
        'gui_app_minimal.py'  # 使用最小化入口文件
    ]
    
    print("运行PyInstaller...")
    print(f"命令: {' '.join(cmd)}")
    
    result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore')
    
    if result.returncode != 0:
        print(f"PyInstaller构建失败:")
        print(result.stderr)
        return False
    
    print("PyInstaller构建成功")
    return True

def copy_additional_files():
    """复制额外的文件"""
    print("复制额外的文件...")
    
    dist_dir = Path('dist/企业微信自动回复系统')
    if not dist_dir.exists():
        print("构建目录不存在")
        return False
    
    # 复制配置文件
    if os.path.exists('config.json'):
        shutil.copy2('config.json', dist_dir)
        print("复制了 config.json")
    elif os.path.exists('config-template.json'):
        shutil.copy2('config-template.json', dist_dir / 'config.json')
        print("复制了配置模板为 config.json")
    
    # 创建启动脚本
    start_script = '''@echo off
chcp 65001 >nul
echo 启动企业微信自动回复系统...

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] 未找到Python环境，请先安装Python 3.10+
    pause
    exit /b 1
)

echo 正在启动程序...
start "" "企业微信自动回复系统.exe"
'''
    
    with open(dist_dir / 'start.bat', 'w', encoding='utf-8') as f:
        f.write(start_script)
    
    # 创建使用说明
    readme_content = '''# 企业微信自动回复系统

## 快速开始：

1. 双击运行 `企业微信自动回复系统.exe` 直接启动程序
2. 或者双击运行 `start.bat` 启动程序

## 注意事项：

- 确保企业微信客户端已安装并登录
- ntwork库已内置在程序目录中
- 配置文件需要根据实际情况修改

## 文件说明：

- `企业微信自动回复系统.exe` - 主程序
- `start.bat` - 启动脚本
- `ntwork/` - ntwork库目录（已内置）
- `config.json` - 配置文件
- `_internal/` - 程序核心文件目录

## 技术支持：

如遇问题请检查：
1. 企业微信客户端是否正常运行
2. 配置文件是否正确设置
'''
    
    with open(dist_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("创建了启动脚本和使用说明")
    return True

def main():
    """主函数"""
    print("企业微信自动回复系统简化打包工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 构建应用
    if not build_with_pyinstaller():
        return False
    
    # 复制ntwork库
    if not copy_ntwork_library():
        print("ntwork库复制失败，但继续构建过程")
    
    # 复制额外文件
    if not copy_additional_files():
        return False
    
    print("\n" + "=" * 60)
    print("单目录exe打包完成！")
    print(f"输出目录: dist/企业微信自动回复系统/")
    print("\n特点:")
    print("- exe文件轻量化，ntwork库作为外部目录")
    print("- ntwork库直接复制到exe目录，无需安装")
    print("- 可以直接运行exe文件")
    print("\n使用说明:")
    print("1. 进入 dist/企业微信自动回复系统/ 目录")
    print("2. 双击运行 企业微信自动回复系统.exe")
    print("3. 或者双击运行 start.bat")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
