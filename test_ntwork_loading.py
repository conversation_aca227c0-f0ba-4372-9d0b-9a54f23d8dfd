#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ntwork库加载的脚本
用于验证修改后的加载路径是否正常工作
"""

import sys
import os

def test_ntwork_loading():
    """测试ntwork库的加载"""
    print("🔍 测试ntwork库加载...")
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本所在目录: {os.path.dirname(os.path.abspath(__file__))}")
    print(f"是否在打包环境: {getattr(sys, 'frozen', False)}")
    
    print("\n📁 当前Python路径:")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    
    # 模拟gui_app.py中的路径初始化
    print("\n🔧 初始化ntwork库路径...")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查是否在PyInstaller打包环境中
    if getattr(sys, 'frozen', False):
        # 在打包的exe中，当前目录就是exe所在目录
        ntwork_path = current_dir
    else:
        # 在开发环境中，使用项目根目录
        ntwork_path = current_dir
    
    # 将ntwork库路径添加到sys.path的最前面，确保优先加载
    if ntwork_path not in sys.path:
        sys.path.insert(0, ntwork_path)
        print(f"✅ 已添加ntwork库搜索路径: {ntwork_path}")
    
    # 检查ntwork目录是否存在
    ntwork_dir = os.path.join(ntwork_path, 'ntwork')
    print(f"\n📂 检查ntwork目录: {ntwork_dir}")
    if os.path.exists(ntwork_dir):
        print("✅ ntwork目录存在")
        
        # 列出ntwork目录内容
        try:
            files = os.listdir(ntwork_dir)
            print(f"📋 ntwork目录内容 ({len(files)} 个文件/目录):")
            for file in sorted(files)[:10]:  # 只显示前10个
                print(f"  - {file}")
            if len(files) > 10:
                print(f"  ... 还有 {len(files) - 10} 个文件")
        except Exception as e:
            print(f"❌ 无法列出ntwork目录内容: {e}")
    else:
        print("❌ ntwork目录不存在")
    
    # 尝试导入ntwork库
    print("\n🔄 尝试导入ntwork库...")
    try:
        import ntwork
        print("✅ ntwork库导入成功")
        print(f"📍 ntwork库位置: {ntwork.__file__}")
        print(f"📦 ntwork库版本: {getattr(ntwork, '__version__', '未知')}")
        
        # 测试基本功能
        try:
            wework = ntwork.WeWork()
            print("✅ WeWork类创建成功")
        except Exception as e:
            print(f"⚠️ WeWork类创建失败: {e}")
            
    except ImportError as e:
        print(f"❌ ntwork库导入失败: {e}")
        print("\n🔍 可能的原因:")
        print("1. ntwork库未正确复制到exe目录")
        print("2. ntwork库依赖的其他模块缺失")
        print("3. Python版本不兼容")
        
    except Exception as e:
        print(f"❌ ntwork库导入时发生其他错误: {e}")
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    test_ntwork_loading()
